import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';

// https://vitejs.dev/config/
export default defineConfig({
  define: {
    global: 'globalThis',
    'process.env': {},
  },
  plugins: [
    react(),
  ],
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      'react-hot-toast',
      'date-fns'
    ],
    exclude: [
      'lucide-react'
    ],
    esbuildOptions: {
      define: {
        global: 'globalThis'
      }
    }
  },
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          router: ['react-router-dom'],
          firebase: ['firebase/app', 'firebase/auth', 'firebase/firestore'],
          ui: ['lucide-react', 'react-hot-toast']
        }
      }
    },
    // Source maps for production debugging
    sourcemap: true,
    // Target modern browsers
    target: 'es2015',
    // Optimize for production
    minify: 'esbuild',
    // Increase chunk size warning limit
    chunkSizeWarningLimit: 1000
  },
  server: {
    port: 5173,
    host: true,
    hmr: {
      overlay: false
    }
  },
  // Set base path for deployment
  base: './',
});
